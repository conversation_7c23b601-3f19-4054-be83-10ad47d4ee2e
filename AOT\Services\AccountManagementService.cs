using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Akka.IO;
using Akka.Util.Internal;
using Grpc.Core;
using HeroYulgang.Core;
using HeroYulgang.Helpers;
using RxjhServer;

namespace HeroYulgang.Services
{
    /// <summary>
    /// gRPC service for account management operations
    /// Service gRPC cho các thao tác quản lý tài khoản
    /// </summary>
    public class AccountManagementService : AccountManagement.AccountManagementBase
    {
        private readonly Logger _logger;

        public AccountManagementService(Logger logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Get detailed information of a specific player by name
        /// L<PERSON>y thông tin chi tiết của một player cụ thể theo tên
        /// </summary>
        public override async Task<GetPlayerDetailResponse> GetPlayerDetail(
            GetPlayerDetailRequest request,
            ServerCallContext context)
        {
            try
            {
                _logger.Info($"Getting player detail for: {request.CharacterName}");

                var response = new GetPlayerDetailResponse
                {
                    Success = false,
                    Message = "Player not found"
                };

                // Find player in connected characters
                var player = World.allConnectedChars.Values
                    .FirstOrDefault(p => p.CharacterName.Equals(request.CharacterName, StringComparison.OrdinalIgnoreCase));

                if (player != null)
                {
                    var playerInfo = CreateDetailedPlayerFullInfo(player);
                    if (playerInfo != null)
                    {
                        response.Success = true;
                        response.Message = "Player found successfully";
                        response.Player = playerInfo;
                        _logger.Info($"Found player: {player.CharacterName} (Level {player.Player_Level})");
                    }
                }
                else
                {
                    _logger.Warning($"Player not found: {request.CharacterName}");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error getting player detail for {request.CharacterName}: {ex.Message}");
                return new GetPlayerDetailResponse
                {
                    Success = false,
                    Message = $"Error retrieving player information: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Get all detailed player information from connected players
        /// Lấy thông tin chi tiết tất cả players từ danh sách kết nối
        /// </summary>
        public override async Task<GetDetailedPlayersResponse> GetDetailedPlayers(
            GetDetailedPlayersRequest request,
            ServerCallContext context)
        {
            try
            {
                _logger.Info("Getting detailed players information");

                var response = new GetDetailedPlayersResponse
                {
                    Success = true,
                    Message = "Players retrieved successfully"
                };

                var allPlayers = new List<DetailedPlayerInfo>();

                // Get all connected players from World.allConnectedChars
                var connectedPlayers = World.allConnectedChars.Values.ToList();
                
                foreach (var player in connectedPlayers)
                {
                    try
                    {
                        var playerInfo = CreateDetailedPlayerInfo(player);
                        if (playerInfo != null)
                        {
                            allPlayers.Add(playerInfo);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning($"Error processing player {player?.CharacterName}: {ex.Message}");
                    }
                }

                // Apply filters
                if (!string.IsNullOrEmpty(request.SearchTerm))
                {
                    var searchTerm = request.SearchTerm.ToLower();
                    allPlayers = allPlayers.Where(p => 
                        p.CharacterName.ToLower().Contains(searchTerm) ||
                        p.AccountId.ToLower().Contains(searchTerm)).ToList();
                }

                if (request.OnlineOnly)
                {
                    allPlayers = allPlayers.Where(p => p.IsOnline).ToList();
                }

                if (request.ServerId > 0)
                {
                    allPlayers = allPlayers.Where(p => p.ServerId == request.ServerId).ToList();
                }

                // Apply sorting
                allPlayers = ApplySorting(allPlayers, request.SortBy, request.SortOrder);

                // Apply pagination
                var totalCount = allPlayers.Count;
                var pageSize = request.PageSize > 0 ? request.PageSize : 50;
                var page = request.Page > 0 ? request.Page : 1;
                var skip = (page - 1) * pageSize;

                var pagedPlayers = allPlayers.Skip(skip).Take(pageSize).ToList();

                response.Players.AddRange(pagedPlayers);
                response.TotalCount = totalCount;
                response.CurrentPage = page;
                response.TotalPages = (int)Math.Ceiling((double)totalCount / pageSize);
                response.OnlineCount = allPlayers.Count(p => p.IsOnline);
                response.PartyCount = GetActivePartyCount();

                _logger.Info($"Retrieved {pagedPlayers.Count} players (page {page}/{response.TotalPages})");

                return response;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error getting detailed players: {ex.Message}");
                return new GetDetailedPlayersResponse
                {
                    Success = false,
                    Message = $"Error retrieving players: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Create detailed player info from Players object
        /// Tạo thông tin chi tiết player từ đối tượng Players
        /// </summary>
        private DetailedPlayerInfo CreateDetailedPlayerInfo(Players player)
        {
            if (player == null) return null;

            var playerInfo = new DetailedPlayerInfo
            {
                AccountId = player.AccountID ?? "",
                CharacterName = player.CharacterName ?? "",
                CharacterIndex = player.CharacterPosition,
                Level = player.Player_Level,
                Job = player.Player_Job,
                JobName = GetJobName(player.Player_Job),
                JobLevel = player.Player_Job_level,
                PosX = player.PosX,
                PosY = player.PosY,
                PosZ = player.PosZ,
                MapId = player.MapID,
                MapName = GetMapName(player.MapID),
                Money = player.Player_Money,
                Experience = player.CharacterExperience.ToString(),
                Hp = player.NhanVat_HP,
                Mp = player.NhanVat_MP,
                Sp = player.NhanVat_SP,
                MaxHp = player.CharacterMax_HP,
                MaxMp = player.CharacterMax_MP,
                MaxSp = player.CharacterMax_SP,
                IsDead = player.PlayerTuVong,
                OffLevel = player.Offline_TreoMay_Mode_ON_OFF,
                OffTrade = 0, // Placeholder - need to find correct property
                IsOnline = true, // All players in allConnectedChars are online
                SessionId = player.SessionID,
                IpAddress = player.LanIp ?? "127.0.0.1",
                LoginTime = player.LoginTime.ToString("yyyy-MM-dd HH:mm:ss"),
                LastActivity = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                ServerId = World.ServerID, // Default server ID
                ClusterId = ConfigManager.Instance.LoginServerSettings.ClusterId, // Default cluster ID
                WuXun = player.Player_WuXun,
                QigongPoint = player.Player_Qigong_point,
                FightExp = 0, // Fight experience (placeholder - need correct property)
                GmMode = player.GMMode,
            };
            LogHelper.WriteLine(LogLevel.Info, $"{player.CharacterName} {World.ServerID} {ConfigManager.Instance.LoginServerSettings.ClusterId}");

            // Add party information if player is in a party
            if (player.TeamID > 0 && World.WToDoi.TryGetValue(player.TeamID, out var party))
            {
                playerInfo.PartyInfo = CreatePartyInfo(party, player);
            }

            return playerInfo;
        }

        /// <summary>
        /// Create detailed player info from Players object
        /// Tạo thông tin chi tiết player từ đối tượng Players
        /// </summary>
        private DetailedPlayerInfoFull CreateDetailedPlayerFullInfo(Players player)
        {
            if (player == null) return null;

            var playerInfo = new DetailedPlayerInfoFull
            {
                AccountId = player.AccountID ?? "",
                CharacterName = player.CharacterName ?? "",
                CharacterIndex = player.CharacterPosition,
                Level = player.Player_Level,
                Job = player.Player_Job,
                JobName = GetJobName(player.Player_Job),
                JobLevel = player.Player_Job_level,
                PosX = player.PosX,
                PosY = player.PosY,
                PosZ = player.PosZ,
                MapId = player.MapID,
                MapName = GetMapName(player.MapID),
                Money = player.Player_Money,
                Experience = player.CharacterExperience.ToString(),
                Hp = player.NhanVat_HP,
                Mp = player.NhanVat_MP,
                Sp = player.NhanVat_SP,
                MaxHp = player.CharacterMax_HP,
                MaxMp = player.CharacterMax_MP,
                MaxSp = player.CharacterMax_SP,
                IsDead = player.PlayerTuVong,
                OffLevel = player.Offline_TreoMay_Mode_ON_OFF,
                OffTrade = 0, // Placeholder - need to find correct property
                IsOnline = true, // All players in allConnectedChars are online
                SessionId = player.SessionID,
                IpAddress = player.LanIp ?? "127.0.0.1",
                LoginTime = player.LoginTime.ToString("yyyy-MM-dd HH:mm:ss"),
                LastActivity = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                ServerId = World.ServerID, // Default server ID
                ClusterId = ConfigManager.Instance.LoginServerSettings.ClusterId, // Default cluster ID
                WuXun = player.Player_WuXun,
                QigongPoint = player.Player_Qigong_point,
                FightExp = 0, // Fight experience (placeholder - need correct property)
                GmMode = player.GMMode,
                Couple = player.FLD_Couple,
                Guild = player.GuildName,
                ThanNuVoCongDiemSo = player.ThanNuVoCongDiemSo,
                Atk = player.FLD_NhanVatCoBan_CongKich,
                Def = player.FLD_NhanVatCoBan_PhongNgu,
                Matk = (int)player.TotalSkillDamage * 10,
                Mdef = player.NhanVat_LonNhat_VoCong_PhongNgu,
                Eva = player.FLD_NhanVatCoBan_NeTranh,
                Acc = player.FLD_NhanVatCoBan_TrungDich,
                MonsterAtk = player.FLD_NhanVat_ThemVao_TanCong_QuaiVat,
                MonsterDef = player.FLD_NhanVat_ThemVao_PhongThu_QuaiVat,
                SkillEva = player.MagicEvade
            };
            playerInfo.WearItems.AddRange(player.Item_Wear.Select(CreateItemInfo));
            playerInfo.SubWearItems.AddRange(player.Sub_Wear.Select(CreateItemInfo));
            playerInfo.ThirdWearItems.AddRange(player.ThietBiTab3.Select(CreateItemInfo));
            playerInfo.InventoryItems.AddRange(player.Item_In_Bag.Select(CreateItemInfo));
            playerInfo.Skills.AddRange(player.VoCongMoi.Cast<X_Vo_Cong_Loai>().Where(skill => skill != null).Select(CreateSkillInfo));
            playerInfo.Abilities.AddRange(player.KhiCong.Select(CreateAbilityInfo));
            playerInfo.PublicWarehouse.AddRange(player.PublicWarehouse.Select(CreateItemInfo));
            playerInfo.PersonalWarehouse.AddRange(player.PersonalWarehouse.Select(CreateItemInfo));
            playerInfo.Medicines.AddRange(player.PublicDrugs.Values.Select(CreateDrugInfo));
            playerInfo.TitleDrugs.AddRange(player.TitleDrug.Values.Select(CreateDrugInfo));
            playerInfo.TimeMedicines.AddRange(player.TimeMedicine.Values.Select(CreateDrugInfo));
            playerInfo.AntiAbilities.AddRange(player.PhanKhiCong.Values.Select(CreateAbilityInfo));
            playerInfo.AscAbilities.AddRange(player.ThangThienKhiCong.Values.Select(CreateAbilityInfo));


            // Add party information if player is in a party
            if (player.TeamID > 0 && World.WToDoi.TryGetValue(player.TeamID, out var party))
            {
                playerInfo.PartyInfo = CreatePartyInfo(party, player);
            }

            return playerInfo;
        }

        private SkillInfo CreateSkillInfo(X_Vo_Cong_Loai skill)
        {
            if (skill == null) return null;

            return new SkillInfo
            {
                SkillId = skill.FLD_PID,
                CurrentLevel = skill.FLD_LEVEL,
                MaxLevel = skill.FLD_ZX,
                Experience = skill.FLD_NEEDEXP,
                SkillType = skill.FLD_TYPE,
            };
        }

        private AbilityInfo CreateAbilityInfo(X_Thang_Thien_Khi_Cong_Loai ability)
        {
            if (ability == null) return null;

            return new AbilityInfo
            {
                AbilityId = ability.KhiCongID,
                CurrentLevel = ability.KhiCong_SoLuong,
                MaxLevel = World.MaxKhiCong_Tren1KhiCong,
            };
        }

        private AbilityInfo CreateAbilityInfo(X_Khi_Cong_Loai ability)
        {
            if (ability == null) return null;

            return new AbilityInfo
            {
                AbilityId = ability.KhiCongID,
                CurrentLevel = ability.KhiCong_SoLuong,
                MaxLevel = World.MaxKhiCong_Tren1KhiCong,
            };
        }
         private DrugInfo CreateDrugInfo(X_Thoi_Gian_Duoc_Pham_Loai drug)
        {
            if (drug == null) return null;

            return new DrugInfo
            {
                DrugId = drug.DuocPhamID,
                DrugTime = (int)drug.ThoiGian,
            };
        }
        

        private DrugInfo CreateDrugInfo(PillItem drug)
        {
            if (drug == null) return null;

            return new DrugInfo
            {
                DrugId = drug.DuocPhamID,
                DrugTime = (int)drug.ThoiGian,
            };
        }

        private DrugInfo CreateDrugInfo(X_Cong_Huu_Duoc_Pham_Loai drug)
        {
            if (drug == null) return null;

            return new DrugInfo
            {
                DrugId = drug.DuocPhamID,
                DrugTime = (int)drug.ThoiGian,
            };
        }

        private ItemInfo CreateItemInfo(Item item)
        {
            if (item == null) return null;

            return new ItemInfo
            {
                GlobalId = item.GetVatPham_ID,
                ItemId = (int)item.GetVatPham_ID,
                Quantity = item.GetVatPhamSoLuong,
                ItemOption = item.FLD_MAGIC0,
                ItemMagic1 = item.FLD_MAGIC1,
                ItemMagic2 = item.FLD_MAGIC2,
                ItemMagic3 = item.FLD_MAGIC3,
                ItemMagic4 = item.FLD_MAGIC4,
                ItemMediumSoul = item.FLD_TuLinh,
                ItemDay1 = item.FLD_DAY1,
                ItemDay2 = item.FLD_DAY2,
                ItemLowSoul = item.FLD_FJ_LowSoul,
                ItemQuality = item.FLD_FJ_TienHoa,
                ItemBeast = item.FLD_TuLinh
            };
        }

        /// <summary>
        /// Create party information
        /// Tạo thông tin party
        /// </summary>
        private PartyInfo CreatePartyInfo(X_To_Doi_Class party, Players currentPlayer)
        {
            if (party == null) return null;

            var partyInfo = new PartyInfo
            {
                TeamId = party.TeamID,
                LeaderName = party.DoiTruongTen ?? "",
                MemberCount = party.ToDoi_NguoiChoi.Count,
                MaxMembers = World.Gioi_han_so_nguoi_vao_party,
                IsLeader = currentPlayer.CharacterName == party.DoiTruongTen
            };

            // Add party members
            foreach (var member in party.ToDoi_NguoiChoi.Values)
            {
                var memberInfo = new PartyMember
                {
                    SessionId = member.SessionID,
                    CharacterName = member.CharacterName ?? "",
                    Level = member.Player_Level,
                    Job = member.Player_Job,
                    IsOnline = true,
                    IsLeader = member.CharacterName == party.DoiTruongTen
                };
                partyInfo.Members.Add(memberInfo);
            }

            return partyInfo;
        }

        /// <summary>
        /// Apply sorting to player list
        /// Áp dụng sắp xếp cho danh sách players
        /// </summary>
        private List<DetailedPlayerInfo> ApplySorting(List<DetailedPlayerInfo> players, string sortBy, string sortOrder)
        {
            var ascending = sortOrder?.ToLower() != "desc";

            return sortBy?.ToLower() switch
            {
                "charactername" => ascending 
                    ? players.OrderBy(p => p.CharacterName).ToList()
                    : players.OrderByDescending(p => p.CharacterName).ToList(),
                "level" => ascending
                    ? players.OrderBy(p => p.Level).ToList()
                    : players.OrderByDescending(p => p.Level).ToList(),
                "job" => ascending
                    ? players.OrderBy(p => p.Job).ToList()
                    : players.OrderByDescending(p => p.Job).ToList(),
                "mapid" => ascending
                    ? players.OrderBy(p => p.MapId).ToList()
                    : players.OrderByDescending(p => p.MapId).ToList(),
                "lastactivity" => ascending
                    ? players.OrderBy(p => p.LastActivity).ToList()
                    : players.OrderByDescending(p => p.LastActivity).ToList(),
                _ => players.OrderBy(p => p.CharacterName).ToList()
            };
        }

        /// <summary>
        /// Get active party count
        /// Lấy số lượng party đang hoạt động
        /// </summary>
        private int GetActivePartyCount()
        {
            try
            {
                return World.WToDoi.Count;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Get job name by job ID
        /// Lấy tên job theo ID
        /// </summary>
        private string GetJobName(int jobId)
        {
            return jobId switch
            {
                1 => "Đao",
                2 => "Kiếm", 
                3 => "Thương",
                4 => "Cung",
                5 => "Đại Phu",
                6 => "Ninja",
                7 => "Cầm sư",
                8 => "Hàn Bảo Quân",
                9 => "Đàm Hoa Liên",
                10 => "Quyền sư",
                11 => "Mai Liễu Chân",
                12 => "Tử Hào",
                13 => "Thần Y",
                _ => "Unknown"
            };
        }

        /// <summary>
        /// Get map name by map ID
        /// Lấy tên map theo ID
        /// </summary>
        private string GetMapName(int mapId)
        {
            try
            {
                return X_Toa_Do_Class.GetName_TiengViet(mapId);
            }
            catch
            {
                return $"Map {mapId}";
            }
        }

        /// <summary>
        /// Save all characters data
        /// Lưu dữ liệu tất cả characters
        /// </summary>
        public override async Task<SaveAllCharactersResponse> SaveAllCharacters(
            SaveAllCharactersRequest request,
            ServerCallContext context)
        {
            try
            {
                _logger.Info($"Starting save all characters operation, force_all: {request.ForceAll}");

                var response = new SaveAllCharactersResponse
                {
                    Success = true,
                    Message = "Save characters operation completed"
                };

                int savedCount = 0;
                int failedCount = 0;
                var failedCharacters = new List<string>();

                // Lặp qua tất cả connected characters trong World.allConnectedChars
                var connectedPlayers = World.allConnectedChars.Values.ToList();

                foreach (var player in connectedPlayers)
                {
                    try
                    {
                        if (player != null && !string.IsNullOrEmpty(player.CharacterName))
                        {
                            // Gọi SaveCharacterData() cho mỗi player
                            player.SaveCharacterData();
                            savedCount++;
                            _logger.Debug($"✓ Saved character: {player.CharacterName} ({player.AccountID})");
                        }
                    }
                    catch (Exception ex)
                    {
                        failedCount++;
                        var characterName = player?.CharacterName ?? "Unknown";
                        failedCharacters.Add(characterName);
                        _logger.Error($"✗ Failed to save character {characterName}: {ex.Message}");
                    }
                }

                response.SavedCount = savedCount;
                response.FailedCount = failedCount;
                response.FailedCharacters.AddRange(failedCharacters);
                response.Message = $"Save completed: {savedCount} successful, {failedCount} failed";

                _logger.Info($"Save characters completed: {savedCount} successful, {failedCount} failed");

                return response;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in SaveAllCharacters: {ex.Message}");
                return new SaveAllCharactersResponse
                {
                    Success = false,
                    Message = $"Error saving characters: {ex.Message}",
                    SavedCount = 0,
                    FailedCount = 0
                };
            }
        }

        /// <summary>
        /// Stop game server
        /// Dừng game server - Lưu tất cả nhân vật trước khi dừng
        /// </summary>
        public override async Task<StopGameServerResponse> StopGameServer(
            StopGameServerRequest request,
            ServerCallContext context)
        {
            try
            {
                _logger.Info($"Starting stop game server operation, graceful: {request.Graceful}, timeout: {request.TimeoutSeconds}s");

                var response = new StopGameServerResponse
                {
                    Success = true,
                    Message = "Stop server operation completed"
                };

                // Lấy số lượng players hiện tại trước khi stop
                var connectedPlayers = World.allConnectedChars.Count;
                response.ConnectedPlayers = connectedPlayers;

                if (World.Instance.State != WorldState.Running)
                {
                    response.Success = false;
                    response.Message = $"Server is not running (current state: {World.Instance.State})";
                    response.ServerState = World.Instance.State.ToString();
                    _logger.Warning($"Server is not running, current state: {World.Instance.State}");
                    return response;
                }

                _logger.Info($"Stopping server with {connectedPlayers} connected players...");

                // BƯỚC 1: Lưu tất cả nhân vật trước khi stop
                _logger.Info("Step 1: Saving all characters before stopping server...");
                int savedCount = 0;
                int failedCount = 0;
                var failedCharacters = new List<string>();

                foreach (var player in World.allConnectedChars.Values.ToList())
                {
                    try
                    {
                        if (player != null && !string.IsNullOrEmpty(player.CharacterName))
                        {
                            // Gọi SaveCharacterData() cho mỗi player
                            player.SaveCharacterData();
                            savedCount++;
                            _logger.Debug($"✓ Saved character: {player.CharacterName} ({player.AccountID})");
                        }
                    }
                    catch (Exception ex)
                    {
                        failedCount++;
                        var characterName = player?.CharacterName ?? "Unknown";
                        failedCharacters.Add(characterName);
                        _logger.Error($"✗ Exception saving character {characterName}: {ex.Message}");
                    }
                }

                _logger.Info($"Character save completed: {savedCount} saved, {failedCount} failed");

                // Nếu có lỗi khi lưu nhân vật, dừng quá trình stop
                if (failedCount > 0)
                {
                    response.Success = false;
                    response.Message = $"Cannot stop server: Failed to save {failedCount} characters. Server stop aborted to prevent data loss.";
                    response.ServerState = "Running";
                    _logger.Error($"✗ Server stop aborted due to character save failures");
                    return response;
                }

                // BƯỚC 2: Thực hiện stop server
                _logger.Info("Step 2: Stopping server...");
                bool stopSuccess = await World.Instance.StopAsync();

                if (stopSuccess)
                {
                    response.Success = true;
                    response.Message = $"Server stopped successfully. {savedCount} characters saved, {connectedPlayers} players disconnected.";
                    response.ServerState = "Stopped";
                    _logger.Info("✓ Server stopped successfully");

                    // BƯỚC 3: Tắt file thực thi
                    _logger.Info("Step 3: Shutting down application...");
                    _ = Task.Run(async () =>
                    {
                        await Task.Delay(2000); // Đợi response được gửi
                        Environment.Exit(0);
                    });
                }
                else
                {
                    response.Success = false;
                    response.Message = "Failed to stop server";
                    response.ServerState = "Error";
                    _logger.Error("✗ Failed to stop server");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in StopGameServer: {ex.Message}");
                return new StopGameServerResponse
                {
                    Success = false,
                    Message = $"Error stopping server: {ex.Message}",
                    ServerState = "Error",
                    ConnectedPlayers = 0
                };
            }
        }

        /// <summary>
        /// Restart game server
        /// Khởi động lại game server - Lưu tất cả nhân vật trước khi restart
        /// </summary>
        public override async Task<RestartGameServerResponse> RestartGameServer(
            RestartGameServerRequest request,
            ServerCallContext context)
        {
            try
            {
                _logger.Info($"Starting restart game server operation, graceful: {request.Graceful}, timeout: {request.TimeoutSeconds}s");

                var startTime = DateTime.Now;
                var response = new RestartGameServerResponse
                {
                    Success = true,
                    Message = "Restart server operation completed"
                };

                if (World.Instance.State != WorldState.Running)
                {
                    response.Success = false;
                    response.Message = $"Server is not running (current state: {World.Instance.State})";
                    response.ServerState = World.Instance.State.ToString();
                    _logger.Warning($"Server is not running, current state: {World.Instance.State}");
                    return response;
                }

                var connectedPlayers = World.allConnectedChars.Count;
                _logger.Info($"Restarting server with {connectedPlayers} connected players...");

                // BƯỚC 1: Lưu tất cả nhân vật trước khi restart
                _logger.Info("Step 1: Saving all characters before restarting server...");
                int savedCount = 0;
                int failedCount = 0;
                var failedCharacters = new List<string>();

                foreach (var player in World.allConnectedChars.Values.ToList())
                {
                    try
                    {
                        if (player != null && !string.IsNullOrEmpty(player.CharacterName))
                        {
                            // Gọi SaveCharacterData() cho mỗi player
                            player.SaveCharacterData();
                            savedCount++;
                            _logger.Debug($"✓ Saved character: {player.CharacterName} ({player.AccountID})");
                        }
                    }
                    catch (Exception ex)
                    {
                        failedCount++;
                        var characterName = player?.CharacterName ?? "Unknown";
                        failedCharacters.Add(characterName);
                        _logger.Error($"✗ Exception saving character {characterName}: {ex.Message}");
                    }
                }

                _logger.Info($"Character save completed: {savedCount} saved, {failedCount} failed");

                // Nếu có lỗi khi lưu nhân vật, dừng quá trình restart
                if (failedCount > 0)
                {
                    response.Success = false;
                    response.Message = $"Cannot restart server: Failed to save {failedCount} characters. Restart aborted to prevent data loss.";
                    response.ServerState = "Running";
                    _logger.Error($"✗ Server restart aborted due to character save failures");
                    return response;
                }

                // BƯỚC 2: Thực hiện restart server với logic cải tiến
                _logger.Info("Step 2: Performing server restart with improved cleanup...");
                await World.Instance.RestartAsync();

                var restartTime = (int)(DateTime.Now - startTime).TotalSeconds;
                response.RestartTimeSeconds = restartTime;

                // Kiểm tra trạng thái server sau restart
                if (World.Instance.State == WorldState.Running)
                {
                    response.Success = true;
                    response.Message = $"Server restarted successfully in {restartTime} seconds. {savedCount} characters saved.";
                    response.ServerState = "Running";
                    _logger.Info($"✓ Server restarted successfully in {restartTime}s");
                }
                else
                {
                    response.Success = false;
                    response.Message = $"Failed to start server after restart (took {restartTime}s). Current state: {World.Instance.State}";
                    response.ServerState = World.Instance.State.ToString();
                    _logger.Error($"✗ Failed to start server after restart, current state: {World.Instance.State}");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in RestartGameServer: {ex.Message}");
                return new RestartGameServerResponse
                {
                    Success = false,
                    Message = $"Error restarting server: {ex.Message}",
                    ServerState = "Error",
                    RestartTimeSeconds = 0
                };
            }
        }

        /// <summary>
        /// Reload config và/hoặc templates
        /// </summary>
        public override async Task<ReloadConfigResponse> ReloadConfig(ReloadConfigRequest request, ServerCallContext context)
        {
            var response = new ReloadConfigResponse
            {
                Success = false,
                Message = "Reload failed",
                ReloadedItems = { }
            };

            try
            {
                _logger.Info($"Received reload config request: Type={request.ConfigType}");

                // Tạo ConfigReloadService và thực hiện reload
                var configReloadService = new ConfigReloadService();
                var result = await configReloadService.ReloadAsync(request.ConfigType);

                response.Success = result.Success;
                response.Message = result.Message;
                response.ReloadedItems.AddRange(result.ReloadedItems);
                response.ReloadTime = Google.Protobuf.WellKnownTypes.Timestamp.FromDateTime(result.ReloadTime.ToUniversalTime());

                if (result.Success)
                {
                    _logger.Info($"✓ Config reload completed successfully. Items: {string.Join(", ", result.ReloadedItems)}");
                }
                else
                {
                    _logger.Error($"✗ Config reload failed: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = $"Reload failed with exception: {ex.Message}";
                _logger.Error($"✗ Config reload failed with exception: {ex.Message}");
            }

            return response;
        }

        /// <summary>
        /// Check Player Online
        /// </summary>

        public override async Task<CheckOnlineResponse> CheckOnline(CheckOnlineRequest request, ServerCallContext context)
        {
            try
            {
                _logger.Info($"Checking online for account {request.AccountId}");
                var isOnline = World.allConnectedChars.Values.Any(p => p.AccountID == request.AccountId);
                return new CheckOnlineResponse
                {
                    Success = true,
                    Message = "Check online completed",
                    IsOnline = isOnline
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"✗ Error checking online: {ex.Message}");
                return new CheckOnlineResponse
                {
                    Success = false,
                    Message = $"Error checking online: {ex.Message}",
                    IsOnline = false
                };
            }
        }

        /// <summary>
        /// Update server list from HeroLogin
        /// </summary>
        public override async Task<UpdateServerListResponse> UpdateServerList(UpdateServerListRequest request, ServerCallContext context)
        {
            try
            {
                _logger.Info($"Updating server list with {request.Servers.Count} servers");

                // Clear existing server list
                World.ServerList.Clear();

                int updatedCount = 0;
                foreach (var serverInfo in request.Servers)
                {
                    var serverList = new ServerList
                    {
                        ServerID = serverInfo.ServerId,
                        ServerIP = serverInfo.ServerIp,
                        ServerPort = serverInfo.ServerPort
                    };

                    World.ServerList[serverInfo.ServerId] = serverList;
                    updatedCount++;

                    _logger.Info($"Updated server {serverInfo.ServerId}: {serverInfo.ServerIp}:{serverInfo.ServerPort} (Online: {serverInfo.IsOnline})");
                }

                _logger.Info($"✓ Server list updated successfully. Total servers: {updatedCount}");

                return new UpdateServerListResponse
                {
                    Success = true,
                    Message = $"Server list updated successfully with {updatedCount} servers",
                    UpdatedCount = updatedCount
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"✗ Error updating server list: {ex.Message}");
                return new UpdateServerListResponse
                {
                    Success = false,
                    Message = $"Error updating server list: {ex.Message}",
                    UpdatedCount = 0
                };
            }
        }

        /// <summary>
        /// Send item to player via mail or direct delivery
        /// Gửi item cho player qua mail hoặc trực tiếp
        /// </summary>
        public override async Task<SendItemToPlayerResponse> SendItemToPlayer(
            SendItemToPlayerRequest request,
            ServerCallContext context)
        {
            try
            {
                _logger.Info($"Sending item to player: {request.CharacterName}, Method: {request.DeliveryMethod}");

                var response = new SendItemToPlayerResponse
                {
                    Success = false,
                    Message = "Player not found",
                    DeliveryMethod = request.DeliveryMethod,
                    PlayerOnline = false
                };

                // Find player in connected characters
                var player = World.allConnectedChars.Values
                    .FirstOrDefault(p => p.CharacterName.Equals(request.CharacterName, StringComparison.OrdinalIgnoreCase));

                bool isPlayerOnline = player != null;
                response.PlayerOnline = isPlayerOnline;

                // Validate delivery method requirements
                if (request.DeliveryMethod == "direct" && !isPlayerOnline)
                {
                    response.Message = "Player must be online for direct delivery";
                    return response;
                }

                // Create item using the new CreateAnItem method
                var item = World.CreateAnItem(
                    request.ItemInfo.ItemId,
                    request.ItemInfo.Quantity,
                    request.ItemInfo.Magic0,
                    request.ItemInfo.Magic1,
                    request.ItemInfo.Magic2,
                    request.ItemInfo.Magic3,
                    request.ItemInfo.Magic4,
                    request.ItemInfo.LowSoul,
                    request.ItemInfo.MediumSoul,
                    request.ItemInfo.TuLinh,
                    request.ItemInfo.Day1,
                    request.ItemInfo.Day2,
                    request.ItemInfo.TienHoa
                );

                if (item == null)
                {
                    response.Message = "Failed to create item";
                    return response;
                }

                if (request.DeliveryMethod == "mail")
                {
                    // Send via mail
                    var description = $"Bạn đã nhận được phần thưởng: {item.GetItemName()} từ [GM]. Vui lòng đến [Bát Quái Lão Nhân] để nhận thưởng";
                    var byteDesc = System.Text.Encoding.GetEncoding(1252).GetBytes(description);

                    var mailResult = World.SendGmMail("[GM]", player.AccountID, byteDesc, 0, item, 30);

                    if (mailResult == 1)
                    {
                        // Send notification if player is online
                        if (isPlayerOnline)
                        {
                            World.SendMailCodNotificationByAdmin(World.ServerID, player.SessionID);
                        }

                        response.Success = true;
                        response.Message = "Item sent via mail successfully";
                    }
                    else
                    {
                        response.Message = "Failed to send mail";
                    }
                }
                else if (request.DeliveryMethod == "direct" && isPlayerOnline)
                {
                    // Send directly to inventory
                    try
                    {
                        var emptySlot = player.GetParcelVacancy(player);
                        if (emptySlot != -1)
                        {
                            player.AddItems(item.ItemGlobal_ID, item.VatPham_ID, emptySlot, item.VatPhamSoLuong, item.VatPham_ThuocTinh);
                            player.HeThongNhacNho($"Bạn đã nhận được {item.GetItemName()} từ [GM]", 7, "TLE");

                            response.Success = true;
                            response.Message = "Item added to inventory successfully";
                        }
                        else
                        {
                            response.Message = "Player inventory is full";
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"Error adding item directly: {ex.Message}");
                        response.Message = "Failed to add item to inventory";
                    }
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.Error($"✗ Error sending item to player: {ex.Message}");
                return new SendItemToPlayerResponse
                {
                    Success = false,
                    Message = $"Error sending item: {ex.Message}",
                    DeliveryMethod = request.DeliveryMethod,
                    PlayerOnline = false
                };
            }
        }

        /// <summary>
        /// Edit player item (sample implementation)
        /// Chỉnh sửa item của player (implementation mẫu)
        /// </summary>
        public override async Task<EditPlayerItemResponse> EditPlayerItem(
            EditPlayerItemRequest request,
            ServerCallContext context)
        {
            try
            {
                _logger.Info($"Editing item for player: {request.CharacterName}, Bag: {request.BagType}, Slot: {request.SlotPosition}");

                var response = new EditPlayerItemResponse
                {
                    Success = false,
                    Message = "Player not found",
                    PlayerOnline = false
                };

                // Find player in connected characters
                var player = World.allConnectedChars.Values
                    .FirstOrDefault(p => p.CharacterName.Equals(request.CharacterName, StringComparison.OrdinalIgnoreCase));

                if (player == null)
                {
                    response.Message = "Player not found or not online";
                    return response;
                }

                response.PlayerOnline = true;

                // Validate bag type (only allow Item_Wear = 0 and Item_In_Bag = 1)
                if (request.BagType != 0 && request.BagType != 1)
                {
                    response.Message = "Invalid bag type. Only Item_Wear (0) and Item_In_Bag (1) are supported";
                    return response;
                }

                // Sample implementation - just return success for now
                // TODO: Implement actual item editing logic
                _logger.Info($"Sample edit implementation for {request.CharacterName} - BagType: {request.BagType}, Slot: {request.SlotPosition}");

                response.Success = true;
                response.Message = "Item edit completed (sample implementation)";

                return response;
            }
            catch (Exception ex)
            {
                _logger.Error($"✗ Error editing player item: {ex.Message}");
                return new EditPlayerItemResponse
                {
                    Success = false,
                    Message = $"Error editing item: {ex.Message}",
                    PlayerOnline = false
                };
            }
        }
    }
}
