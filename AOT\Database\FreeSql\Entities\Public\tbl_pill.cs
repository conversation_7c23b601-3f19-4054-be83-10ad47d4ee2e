﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_pill {

		[JsonProperty]
		public int id { get; set; }

		[JsonProperty]
		public int pill_id { get; set; }

		[JsonProperty, Column(DbType = "varchar")]
		public string pill_name { get; set; }

		[JsonProperty, Column(DbType = "varchar")]
		public string level_use { get; set; }

		[JsonProperty]
		public int bonus_hp { get; set; }

		[JsonProperty]
		public int bonus_hppercent { get; set; }

		[JsonProperty]
		public int bonus_mp { get; set; }

		[JsonProperty]
		public int bonus_mppercent { get; set; }

		[JsonProperty]
		public int bonus_atk { get; set; }

		[JsonProperty]
		public int bonus_atkpercent { get; set; }

		[JsonProperty]
		public int bonus_df { get; set; }

		[JsonProperty]
		public int bonus_dfpercent { get; set; }

		[JsonProperty]
		public int bonus_evasion { get; set; }

		[JsonProperty]
		public int bonus_evapercent { get; set; }

		[JsonProperty]
		public int bonus_accuracy { get; set; }

		[JsonProperty]
		public int bonus_accupercent { get; set; }

		[JsonProperty]
		public int bonus_atkskillpercent { get; set; }

		[JsonProperty]
		public int bonus_dfskill { get; set; }

		[JsonProperty]
		public int bonus_dfskillpercent { get; set; }

		[JsonProperty]
		public int bonus_abilities { get; set; }

		[JsonProperty]
		public int bonus_lucky { get; set; }

		[JsonProperty]
		public int bonus_goldpercent { get; set; }

		[JsonProperty]
		public int bonus_droppercent { get; set; }

		[JsonProperty]
		public int bonus_exppercent { get; set; }

		[JsonProperty]
		public int upgrade_weapon { get; set; }

		[JsonProperty]
		public int upgrade_armor { get; set; }

		[JsonProperty]
		public int pill_time { get; set; }

		[JsonProperty]
		public int pill_days { get; set; }

		[JsonProperty]
		public int public_pill { get; set; }

		[JsonProperty]
		public int pill_merge { get; set; }

		[JsonProperty, Column(DbType = "varchar")]
		public string cant_use { get; set; }

		[JsonProperty]
		public int on_off { get; set; }

		[JsonProperty]
		public int hatch_item { get; set; }

		[JsonProperty]
		public int bonus_diemhoangkim { get; set; }

		[JsonProperty]
		public int tanghoa { get; set; }

	}

}
