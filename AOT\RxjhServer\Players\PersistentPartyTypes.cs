using System;
using System.Collections.Generic;

namespace RxjhServer;

/// <summary>
/// Party persistence modes
/// </summary>
public enum PartyPersistenceMode
{
    /// <summary>
    /// Dissolves when all members disconnect
    /// </summary>
    Temporary = 0,
    
    /// <summary>
    /// Survives disconnects for 24 hours
    /// </summary>
    Persistent = 1,
    
    /// <summary>
    /// Survives until manually disbanded
    /// </summary>
    Permanent = 2
}

/// <summary>
/// Reasons for leaving party
/// </summary>
public enum PartyLeaveReason
{
    /// <summary>
    /// Player chose to leave
    /// </summary>
    Voluntary = 0,
    
    /// <summary>
    /// Network disconnect
    /// </summary>
    Disconnect = 1,
    
    /// <summary>
    /// Kicked by leader
    /// </summary>
    Kicked = 2,
    
    /// <summary>
    /// Server maintenance/shutdown
    /// </summary>
    ServerShutdown = 3
}

/// <summary>
/// Offline party member data
/// </summary>
public class OfflinePartyMember
{
    /// <summary>
    /// Character name
    /// </summary>
    public string CharacterName { get; set; }
    
    /// <summary>
    /// Account ID
    /// </summary>
    public int AccountId { get; set; }
    
    /// <summary>
    /// Original join order in party
    /// </summary>
    public int OriginalJoinOrder { get; set; }
    
    /// <summary>
    /// When player joined the party
    /// </summary>
    public DateTime JoinedAt { get; set; }
    
    /// <summary>
    /// When player disconnected
    /// </summary>
    public DateTime DisconnectedAt { get; set; }
    
    /// <summary>
    /// Was this player the leader
    /// </summary>
    public bool IsLeader { get; set; }
    
    /// <summary>
    /// Server ID where player was last seen
    /// </summary>
    public int ServerId { get; set; }
    
    /// <summary>
    /// Player level when disconnected
    /// </summary>
    public int Level { get; set; }
    
    /// <summary>
    /// Player job when disconnected
    /// </summary>
    public int Job { get; set; }
    
    /// <summary>
    /// How long has player been offline
    /// </summary>
    public TimeSpan OfflineDuration => DateTime.Now - DisconnectedAt;
    
    /// <summary>
    /// Is offline member expired (offline > 24h)
    /// </summary>
    public bool IsExpired => OfflineDuration > TimeSpan.FromHours(24);
}

/// <summary>
/// Party statistics for monitoring
/// </summary>
public class PartyStatistics
{
    /// <summary>
    /// Total number of active parties
    /// </summary>
    public int TotalActiveParties { get; set; }
    
    /// <summary>
    /// Total online members across all parties
    /// </summary>
    public int TotalOnlineMembers { get; set; }
    
    /// <summary>
    /// Total offline members across all parties
    /// </summary>
    public int TotalOfflineMembers { get; set; }
    
    /// <summary>
    /// Parties by persistence mode
    /// </summary>
    public Dictionary<PartyPersistenceMode, int> PartiesByMode { get; set; } = new();
    
    /// <summary>
    /// Parties by server
    /// </summary>
    public Dictionary<int, int> PartiesByServer { get; set; } = new();
    
    /// <summary>
    /// Average party size
    /// </summary>
    public double AveragePartySize { get; set; }
    
    /// <summary>
    /// Parties with only offline members
    /// </summary>
    public int OfflineOnlyParties { get; set; }
    
    /// <summary>
    /// Expired parties (should be cleaned up)
    /// </summary>
    public int ExpiredParties { get; set; }
}
