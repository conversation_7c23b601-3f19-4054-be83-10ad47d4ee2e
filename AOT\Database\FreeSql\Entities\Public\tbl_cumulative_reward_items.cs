using FreeSql.DatabaseModel;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public
{
    [JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
    public partial class tbl_cumulative_reward_items
    {
        [JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('tbl_cumulative_reward_items_id_seq'::regclass)")]
        public int id { get; set; }

        [JsonProperty]
        public int template_id { get; set; }

        [JsonProperty]
        public int milestone_number { get; set; }

        [JsonProperty]
        public int item_slot { get; set; }

        [JsonProperty]
        public int item_id { get; set; }

        [JsonProperty]
        public int item_amount { get; set; } = 1;
    }
}
