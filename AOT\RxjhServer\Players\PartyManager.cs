using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading.Tasks;
using HeroYulgang.Helpers;

namespace RxjhServer;

/// <summary>
/// Simplified Party Management System
/// Core functionality only - replaces WToDoi and RestoredParties
/// </summary>
public static class PartyManager
{
    #region Core Properties

    /// <summary>
    /// Active parties (in-memory, includes both online and offline members)
    /// Key: TeamID, Value: Enhanced X_To_Doi_Class
    /// </summary>
    public static ConcurrentDictionary<int, X_To_Doi_Class> ActiveParties { get; private set; }

    /// <summary>
    /// Fast lookup: PlayerName -> TeamID (most important index)
    /// </summary>
    public static ConcurrentDictionary<string, int> PlayerPartyIndex { get; private set; }

    /// <summary>
    /// Next available TeamID
    /// </summary>
    private static int _nextTeamId = 1;

    /// <summary>
    /// Lock for TeamID generation
    /// </summary>
    private static readonly object _teamIdLock = new();

    /// <summary>
    /// Track parties that need to be saved (dirty flag)
    /// Key: PartyUUID, Value: true if needs saving
    /// </summary>
    private static readonly ConcurrentDictionary<string, bool> _dirtyParties = new();

    #endregion

    #region Initialization

    /// <summary>
    /// Initialize PartyManager
    /// </summary>
    public static void Initialize()
    {
        ActiveParties = new ConcurrentDictionary<int, X_To_Doi_Class>();
        PlayerPartyIndex = new ConcurrentDictionary<string, int>();

        LogHelper.WriteLine(LogLevel.Info, "SimplePartyManager initialized");
    }

    /// <summary>
    /// Load persistent parties from database
    /// </summary>
    public static async Task LoadPersistentPartiesAsync()
    {
        try
        {
            var parties = await PartyDatabase.LoadPersistentPartiesAsync(World.ServerID);
            
            foreach (var party in parties)
            {
                // Check for TeamID conflicts and reassign if needed
                if (ActiveParties.ContainsKey(party.TeamID))
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"TeamID conflict detected for party {party.PartyUUID}, reassigning...");

                    lock (_teamIdLock)
                    {
                        party.TeamID = _nextTeamId++;
                    }

                    // Update all online members' TeamID
                    foreach (var player in party.ToDoi_NguoiChoi.Values)
                    {
                        player.TeamID = party.TeamID;
                    }
                }

                // Simple registration - just add to collections
                ActiveParties.TryAdd(party.TeamID, party);

                // Index all members (online + offline)
                foreach (var player in party.ToDoi_NguoiChoi.Values)
                {
                    PlayerPartyIndex.TryAdd(player.CharacterName, party.TeamID);
                }
                foreach (var offlineMember in party.OfflineMembers.Values)
                {
                    PlayerPartyIndex.TryAdd(offlineMember.CharacterName, party.TeamID);
                }

                // Update next TeamID
                if (party.TeamID >= _nextTeamId)
                    _nextTeamId = party.TeamID + 1;
            }
            
            LogHelper.WriteLine(LogLevel.Info, $"✓ Loaded {ActiveParties.Count} persistent parties");
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"✗ Failed to load persistent parties: {ex.Message}");
        }
    }

    /// <summary>
    /// Mark party as dirty (needs saving)
    /// </summary>
    private static void MarkPartyDirty(X_To_Doi_Class party)
    {
        if (party == null || string.IsNullOrEmpty(party.PartyUUID))
            return;

        _dirtyParties.TryAdd(party.PartyUUID, true);
    }

    /// <summary>
    /// Save party immediately (for voluntary actions only)
    /// </summary>
    private static void SavePartyImmediately(X_To_Doi_Class party)
    {
        if (party == null || string.IsNullOrEmpty(party.PartyUUID))
            return;

        _ = Task.Run(() => PartyDatabase.SavePartyAsync(party));
        _dirtyParties.TryRemove(party.PartyUUID, out _);
    }

    #endregion

    #region Core Operations

    /// <summary>
    /// Create new party (simplified)
    /// </summary>
    public static X_To_Doi_Class CreateParty(Players leader)
    {
        try
        {
            // Check if player is already in a party
            if (leader.TeamID != 0)
            {
                LogHelper.WriteLine(LogLevel.Warning, $"Player {leader.CharacterName} already in party {leader.TeamID}");
                return null;
            }

            var party = new X_To_Doi_Class(leader);

            // Assign TeamID
            lock (_teamIdLock)
            {
                party.TeamID = _nextTeamId++;
            }

            // Set player's team info
            leader.TeamID = party.TeamID;
            leader.TeamingStage = 2;

            // Add to collections
            ActiveParties.TryAdd(party.TeamID, party);
            PlayerPartyIndex.TryAdd(leader.CharacterName, party.TeamID);

            // Mark party as dirty for scheduled save
            MarkPartyDirty(party);

            LogHelper.WriteLine(LogLevel.Info, $"Created party {party.PartyUUID} (TeamID: {party.TeamID}) for {leader.CharacterName}");
            return party;
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Error creating party: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// Player joins party (simplified)
    /// </summary>
    public static bool JoinParty(int teamID, Players player)
    {
        try
        {
            if (!ActiveParties.TryGetValue(teamID, out var party))
            {
                LogHelper.WriteLine(LogLevel.Warning, $"Party {teamID} not found for join request");
                return false;
            }

            // Check if player is already in another party
            if (player.TeamID != 0 && player.TeamID != teamID)
            {
                LogHelper.WriteLine(LogLevel.Warning, $"Player {player.CharacterName} already in party {player.TeamID}");
                return false;
            }

            // Check if player was offline member
            if (party.OfflineMembers.TryRemove(player.CharacterName, out var offlineMember))
            {
                // Player rejoining - restore to online
                party.ToDoi_NguoiChoi.TryAdd(player.SessionID, player);
                player.TeamID = teamID;
                player.TeamingStage = 2;

                LogHelper.WriteLine(LogLevel.Info, $"Player {player.CharacterName} rejoined party {party.PartyUUID}");
            }
            else
            {
                // New member joining
                if (party.ToDoi_NguoiChoi.Count >= World.Gioi_han_so_nguoi_vao_party)
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"Party {teamID} is full");
                    return false;
                }

                party.ToDoi_NguoiChoi.TryAdd(player.SessionID, player);
                player.TeamID = teamID;
                player.TeamingStage = 2;

                LogHelper.WriteLine(LogLevel.Info, $"Player {player.CharacterName} joined party {party.PartyUUID}");
            }

            // Update index and activity
            PlayerPartyIndex.TryAdd(player.CharacterName, teamID);
            party.LastActiveAt = DateTime.Now;

            // Show party UI if we have enough members
            if (party.ToDoi_NguoiChoi.Count >= 2)
            {
                foreach (var onlineMember in party.ToDoi_NguoiChoi.Values)
                {
                    onlineMember.ShowPlayers();
                }
            }

            // Mark party as dirty for scheduled save (not immediate save)
            MarkPartyDirty(party);

            return true;
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Error joining party: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Player leaves party (simplified)
    /// </summary>
    public static void LeaveParty(Players player, PartyLeaveReason reason)
    {
        try
        {
            if (player.TeamID == 0 || !ActiveParties.TryGetValue(player.TeamID, out var party))
                return;

            // Remove from online members
            party.ToDoi_NguoiChoi.TryRemove(player.SessionID, out _);
            PlayerPartyIndex.TryRemove(player.CharacterName, out _);

            if (reason == PartyLeaveReason.Disconnect && party.IsPersistent)
            {
                // Move to offline members (disconnect - not voluntary)
                party.OfflineMembers.TryAdd(player.CharacterName, new OfflinePartyMember
                {
                    CharacterName = player.CharacterName,
                    AccountId = int.Parse(player.AccountID),
                    OriginalJoinOrder = 1,
                    JoinedAt = DateTime.Now,
                    DisconnectedAt = DateTime.Now,
                    IsLeader = player.CharacterName == party.DoiTruongTen,
                    ServerId = World.ServerID,
                    Level = player.Player_Level,
                    Job = player.Player_Job
                });

                // Re-add to player index for offline member
                PlayerPartyIndex.TryAdd(player.CharacterName, player.TeamID);

                LogHelper.WriteLine(LogLevel.Info, $"Player {player.CharacterName} moved to offline in party {party.PartyUUID}");

                // Only mark dirty for disconnect (will be saved on schedule)
                MarkPartyDirty(party);
            }
            else
            {
                // Voluntary leave - save immediately
                LogHelper.WriteLine(LogLevel.Info, $"Player {player.CharacterName} voluntarily left party {party.PartyUUID}");
                SavePartyImmediately(party);
            }

            // Reset player party info
            player.TeamID = 0;
            player.TeamingStage = 0;

            // Simple validity check - dissolve if no members left
            var totalMembers = party.ToDoi_NguoiChoi.Count + party.OfflineMembers.Count;
            if (totalMembers == 0)
            {
                DissolveParty(party.TeamID, "no members");
            }
            else
            {
                // Update activity and mark dirty for scheduled save
                party.LastActiveAt = DateTime.Now;
                MarkPartyDirty(party);
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Error leaving party: {ex.Message}");
        }
    }

    /// <summary>
    /// Dissolve party completely (simplified)
    /// </summary>
    public static void DissolveParty(int teamID, string reason)
    {
        try
        {
            if (!ActiveParties.TryRemove(teamID, out var party))
                return;

            // Remove online members from index and reset their party info
            foreach (var player in party.ToDoi_NguoiChoi.Values)
            {
                PlayerPartyIndex.TryRemove(player.CharacterName, out _);
                player.TeamID = 0;
                player.TeamingStage = 0;
            }

            // Remove offline members from index
            foreach (var offlineMember in party.OfflineMembers.Values)
            {
                PlayerPartyIndex.TryRemove(offlineMember.CharacterName, out _);
            }

            // Save immediately since dissolve is a voluntary action
            SavePartyImmediately(party);

            LogHelper.WriteLine(LogLevel.Info, $"Dissolved party {party.PartyUUID} (TeamID: {teamID}) - {reason}");
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Error dissolving party: {ex.Message}");
        }
    }

    /// <summary>
    /// Get party by player name - O(1)
    /// </summary>
    public static X_To_Doi_Class GetPartyByPlayer(string playerName)
    {
        if (string.IsNullOrEmpty(playerName))
            return null;

        if (PlayerPartyIndex.TryGetValue(playerName, out var teamID))
        {
            ActiveParties.TryGetValue(teamID, out var party);
            return party;
        }
        return null;
    }

    /// <summary>
    /// Save all dirty parties (called from ProcessSaveAllCharacters)
    /// </summary>
    public static async Task SaveAllDirtyPartiesAsync()
    {
        try
        {
            var dirtyPartyUUIDs = _dirtyParties.Keys.ToList();
            var savedCount = 0;

            foreach (var partyUUID in dirtyPartyUUIDs)
            {
                // Find the party in ActiveParties
                var party = ActiveParties.Values.FirstOrDefault(p => p.PartyUUID == partyUUID);
                if (party != null)
                {
                    await PartyDatabase.SavePartyAsync(party);
                    _dirtyParties.TryRemove(partyUUID, out _);
                    savedCount++;
                }
                else
                {
                    // Party no longer exists, remove from dirty list
                    _dirtyParties.TryRemove(partyUUID, out _);
                }
            }

            if (savedCount > 0)
            {
                LogHelper.WriteLine(LogLevel.Debug, $"Saved {savedCount} dirty parties to database");
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Error saving dirty parties: {ex.Message}");
        }
    }

    /// <summary>
    /// Enhanced restore conditions - only restore if 2+ members online and no current party
    /// </summary>
    public static bool CanRestoreParty(X_To_Doi_Class persistentParty, Players player)
    {
        try
        {
            // Player must not be in any party currently
            if (player.TeamID != 0)
                return false;

            // Count how many members from persistent party are currently online and not in other parties
            var onlineEligibleMembers = 0;

            // Check online members
            foreach (var onlineMember in persistentParty.ToDoi_NguoiChoi.Values)
            {
                if (onlineMember.TeamID == 0) // Not in another party
                    onlineEligibleMembers++;
            }

            // Check offline members who are now online
            foreach (var offlineMember in persistentParty.OfflineMembers.Values)
            {
                var onlinePlayer = World.KiemTra_Ten_NguoiChoi(offlineMember.CharacterName);
                if (onlinePlayer != null && onlinePlayer.TeamID == 0) // Online and not in another party
                    onlineEligibleMembers++;
            }

            // Need at least 2 eligible members to restore
            return onlineEligibleMembers >= 2;
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Error checking restore conditions: {ex.Message}");
            return false;
        }
    }

    #endregion
}
