﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_hatchitem {

		[JsonProperty]
		public int ID { get; set; }

		[JsonProperty]
		public int FLD_PID { get; set; }

		[JsonProperty, Column(StringLength = 200)]
		public string FLD_NAME { get; set; }

		[JsonProperty]
		public int FLD_PIDX { get; set; }

		[JsonProperty, Column(StringLength = 200)]
		public string FLD_NAMEX { get; set; }

		[JsonProperty]
		public int FLD_Number { get; set; }

		[JsonProperty]
		public int FLD_PP { get; set; }

		[JsonProperty]
		public int FLD_MAGIC0 { get; set; }

		[JsonProperty]
		public int FLD_MAGIC1 { get; set; }

		[JsonProperty]
		public int FLD_MAGIC2 { get; set; }

		[JsonProperty]
		public int FLD_MAGIC3 { get; set; }

		[JsonProperty]
		public int FLD_MAGIC4 { get; set; }

		[JsonProperty]
		public int FLD_LowSoul { get; set; }

		[JsonProperty]
		public int FLD_MedSoul { get; set; }

		[JsonProperty]
		public int FLD_Quality { get; set; }

		[JsonProperty]
		public int FLD_Lock { get; set; }

		[JsonProperty]
		public int FLD_ExpiryDate { get; set; }

		[JsonProperty]
		public int FLD_Announce { get; set; }

	}

}
