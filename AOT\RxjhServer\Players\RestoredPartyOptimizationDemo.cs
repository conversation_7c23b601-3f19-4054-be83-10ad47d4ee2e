using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace RxjhServer;

/// <summary>
/// Demo class để test hiệu suất của hệ thống RestoredParties đã được tối ưu
/// </summary>
public static class RestoredPartyOptimizationDemo
{
    /// <summary>
    /// Test hiệu suất của hệ thống cũ vs mới
    /// </summary>
    public static void RunPerformanceTest()
    {
        Console.WriteLine("=== RestoredParties Performance Test ===");
        
        // Tạo test data
        var testParties = CreateTestData(1000, 5); // 1000 parties, 5 members each
        
        // Test old method (linear search)
        var oldTime = TestOldMethod(testParties, "TestPlayer2500"); // Player ở giữa
        
        // Test new method (indexed lookup)
        var newTime = TestNewMethod(testParties, "TestPlayer2500");
        
        Console.WriteLine($"Old Method Time: {oldTime.TotalMilliseconds:F2}ms");
        Console.WriteLine($"New Method Time: {newTime.TotalMilliseconds:F2}ms");
        Console.WriteLine($"Performance Improvement: {oldTime.TotalMilliseconds / newTime.TotalMilliseconds:F1}x faster");
        
        // Test worst case (player not found)
        var oldTimeNotFound = TestOldMethod(testParties, "NonExistentPlayer");
        var newTimeNotFound = TestNewMethod(testParties, "NonExistentPlayer");
        
        Console.WriteLine($"\nWorst Case (Player Not Found):");
        Console.WriteLine($"Old Method Time: {oldTimeNotFound.TotalMilliseconds:F2}ms");
        Console.WriteLine($"New Method Time: {newTimeNotFound.TotalMilliseconds:F2}ms");
        Console.WriteLine($"Performance Improvement: {oldTimeNotFound.TotalMilliseconds / newTimeNotFound.TotalMilliseconds:F1}x faster");
    }
    
    /// <summary>
    /// Tạo test data
    /// </summary>
    private static List<RestoredPartyData> CreateTestData(int partyCount, int membersPerParty)
    {
        var parties = new List<RestoredPartyData>();
        
        for (int i = 0; i < partyCount; i++)
        {
            var party = new RestoredPartyData
            {
                PartyUUID = Guid.NewGuid().ToString(),
                OriginalLeaderName = $"Leader{i}",
                CurrentLeaderName = $"Leader{i}",
                LootType = 1,
                MaxMembers = 8,
                ServerId = 1,
                RestoredAt = DateTime.Now,
                LastActiveAt = DateTime.Now,
                Members = new List<RestoredPartyMember>()
            };
            
            for (int j = 0; j < membersPerParty; j++)
            {
                party.Members.Add(new RestoredPartyMember
                {
                    CharacterName = $"TestPlayer{i * membersPerParty + j}",
                    AccountId = i * membersPerParty + j,
                    OriginalJoinOrder = j + 1,
                    JoinedAt = DateTime.Now,
                    IsLeader = j == 0
                });
            }
            
            parties.Add(party);
        }
        
        return parties;
    }
    
    /// <summary>
    /// Test phương thức cũ (linear search)
    /// </summary>
    private static TimeSpan TestOldMethod(List<RestoredPartyData> parties, string playerName)
    {
        var stopwatch = Stopwatch.StartNew();
        
        // Simulate old method: O(n*m) complexity
        for (int iteration = 0; iteration < 1000; iteration++)
        {
            var found = parties.FirstOrDefault(p => 
                p.Members.Any(m => m.CharacterName == playerName));
        }
        
        stopwatch.Stop();
        return stopwatch.Elapsed;
    }
    
    /// <summary>
    /// Test phương thức mới (indexed lookup)
    /// </summary>
    private static TimeSpan TestNewMethod(List<RestoredPartyData> parties, string playerName)
    {
        // Setup index
        var playerIndex = new ConcurrentDictionary<string, string>();
        var partyDict = new ConcurrentDictionary<string, RestoredPartyData>();
        
        foreach (var party in parties)
        {
            partyDict.TryAdd(party.PartyUUID, party);
            foreach (var member in party.Members)
            {
                playerIndex.TryAdd(member.CharacterName, party.PartyUUID);
            }
        }
        
        var stopwatch = Stopwatch.StartNew();
        
        // Simulate new method: O(1) complexity
        for (int iteration = 0; iteration < 1000; iteration++)
        {
            RestoredPartyData found = null;
            if (playerIndex.TryGetValue(playerName, out var partyUUID))
            {
                partyDict.TryGetValue(partyUUID, out found);
            }
        }
        
        stopwatch.Stop();
        return stopwatch.Elapsed;
    }
    
    /// <summary>
    /// Test memory usage của hệ thống mới
    /// </summary>
    public static void TestMemoryUsage()
    {
        Console.WriteLine("\n=== Memory Usage Test ===");
        
        var parties = CreateTestData(10000, 8); // 10k parties, 8 members each = 80k players
        
        // Calculate memory for indexes
        var playerIndexMemory = parties.Sum(p => p.Members.Count) * (50 + 36); // ~86 bytes per entry (string + UUID)
        var teamIndexMemory = parties.Count * (4 + 36); // ~40 bytes per entry (int + UUID)
        
        var totalIndexMemory = (playerIndexMemory + teamIndexMemory) / 1024.0 / 1024.0; // Convert to MB
        
        Console.WriteLine($"Test Data: {parties.Count:N0} parties, {parties.Sum(p => p.Members.Count):N0} total players");
        Console.WriteLine($"Estimated Index Memory Usage: {totalIndexMemory:F2} MB");
        Console.WriteLine($"Memory per player: {(totalIndexMemory * 1024 * 1024) / parties.Sum(p => p.Members.Count):F0} bytes");
        
        // Performance comparison
        var oldComplexity = parties.Count * parties.Average(p => p.Members.Count);
        Console.WriteLine($"\nComplexity Comparison:");
        Console.WriteLine($"Old Method: O(n*m) = {oldComplexity:N0} operations worst case");
        Console.WriteLine($"New Method: O(1) = 1 operation");
        Console.WriteLine($"Theoretical speedup: {oldComplexity:N0}x");
    }
    
    /// <summary>
    /// Demo các tính năng mới
    /// </summary>
    public static void DemoNewFeatures()
    {
        Console.WriteLine("\n=== New Features Demo ===");
        
        Console.WriteLine("✓ Fast player lookup: O(1) instead of O(n*m)");
        Console.WriteLine("✓ Fast team lookup: O(1) instead of O(n)");
        Console.WriteLine("✓ Automatic index maintenance");
        Console.WriteLine("✓ ActiveTeamID tracking for better party management");
        Console.WriteLine("✓ Memory efficient indexing");
        Console.WriteLine("✓ Thread-safe operations with ConcurrentDictionary");
        
        Console.WriteLine("\nBefore optimization:");
        Console.WriteLine("- CheckAndRestoreParty(): O(n*m*k) complexity");
        Console.WriteLine("- Had to search through all parties and members");
        Console.WriteLine("- No direct TeamID to RestoredParty mapping");
        
        Console.WriteLine("\nAfter optimization:");
        Console.WriteLine("- CheckAndRestoreParty(): O(1) complexity");
        Console.WriteLine("- Direct hash table lookup");
        Console.WriteLine("- TeamID index for fast reverse lookup");
        Console.WriteLine("- Automatic index synchronization");
    }
}
