using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class X_To_Doi_Class : IDisposable
{
	public List<Players> tem = new();

	public ConcurrentDictionary<int, Players> ToDoi_NguoiChoi;

	private ConcurrentDictionary<string, X_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>i_Tuyen> To<PERSON><PERSON>_NguoiChoi_Da_OffLine;

	public string DoiTruongTen;

	public int TeamID;

	public Players <PERSON><PERSON>_NguoiChoi;

	public Players DoiTruong;

	public int DoiNguDangCap;

	public bool RedPackage;

	public int RedPackageThoiGian;

	public int DaoCuQuyTac_PhanPhoi;

	public int PhanBoHienTai;

	public System.Timers.Timer AutomaticDisplay;

	private int GuiLoiMoi_ToDoi_DemSoLuong;

	#region Persistent Party Properties

	/// <summary>
	/// Unique party identifier (persists across server restarts)
	/// </summary>
	public string PartyUUID { get; set; }

	/// <summary>
	/// Server ID where party was created
	/// </summary>
	public int ServerId { get; set; }

	/// <summary>
	/// Party creation time
	/// </summary>
	public DateTime CreatedAt { get; set; }

	/// <summary>
	/// Last activity time (auto-updated)
	/// </summary>
	public DateTime LastActiveAt { get; set; }

	/// <summary>
	/// Offline members (disconnected but still in party)
	/// </summary>
	public ConcurrentDictionary<string, OfflinePartyMember> OfflineMembers { get; set; }

	/// <summary>
	/// Party persistence mode
	/// </summary>
	public PartyPersistenceMode PersistenceMode { get; set; }

	/// <summary>
	/// Is party persistent (survives disconnects)
	/// </summary>
	public bool IsPersistent => PersistenceMode != PartyPersistenceMode.Temporary;

	/// <summary>
	/// Total members (online + offline)
	/// </summary>
	public int TotalMemberCount => ToDoi_NguoiChoi.Count + OfflineMembers.Count;

	#endregion

	public Players Leader => World.allConnectedChars.Values.Where((Players K) => K.CharacterName == DoiTruongTen).FirstOrDefault();

	public X_To_Doi_Class(Players Doi_Truong)
	{
		AutomaticDisplay = new(5000.0);
		AutomaticDisplay.Elapsed += AutomaticDisplayEvent;
		AutomaticDisplay.AutoReset = true;
		DoiTruongTen = Doi_Truong.CharacterName;
		DoiTruong = Doi_Truong;
		ToDoi_NguoiChoi = new();
		ToDoi_NguoiChoi.TryAdd(Doi_Truong.SessionID, Doi_Truong);
		PhanBoHienTai = 0;
		DaoCuQuyTac_PhanPhoi = 1;
		RedPackage = false;
		RedPackageThoiGian = 0;
		DoiNguDangCap = Doi_Truong.Player_Level;
		ToDoi_NguoiChoi_Da_OffLine = new();

		// Initialize persistent party properties
		PartyUUID = Guid.NewGuid().ToString();
		ServerId = World.ServerID;
		CreatedAt = DateTime.Now;
		LastActiveAt = DateTime.Now;
		OfflineMembers = new ConcurrentDictionary<string, OfflinePartyMember>();
		PersistenceMode = PartyPersistenceMode.Persistent; // Default to persistent
	}

	~X_To_Doi_Class()
	{
	}

	public Players ThanhVien_DatDuoc_TuongUng(int key)
	{
		try
		{
			var num = 0;
			foreach (var value in ToDoi_NguoiChoi.Values)
			{
				if (key == num)
				{
					return value;
				}
				num++;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Nhận tương ứng party NguoiChoi error!" + ex.Message);
		}
		return null;
	}

	public void Dispose()
	{
		try
		{
			if (ToDoi_NguoiChoi_Da_OffLine != null)
			{
				ToDoi_NguoiChoi_Da_OffLine.Clear();
			}
			// Dissolve party using PartyManager
			PartyManager.DissolveParty(TeamID, "party disbanded");
			if (ToDoi_NguoiChoi != null)
			{
				foreach (var value2 in ToDoi_NguoiChoi.Values)
				{
					value2.GiaiTan_ToDoi_NhacNho();
					value2.TeamID = 0;
					value2.TeamingStage = 0;
					value2.CoupleInTeam = false;
				}
			}
			if (ToDoi_NguoiChoi != null)
			{
				ToDoi_NguoiChoi.Clear();
			}
			if (AutomaticDisplay != null)
			{
				AutomaticDisplay.Enabled = false;
				AutomaticDisplay.Close();
				AutomaticDisplay.Dispose();
				AutomaticDisplay = null;
			}
			Moi_NguoiChoi = null;
			tem = null;
			DoiNguDangCap = 0;
			RedPackage = false;
			RedPackageThoiGian = 0;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "ToDoi tốt bụng Dispose error!" + ex.Message);
		}
		finally
		{
            tem?.Clear();
            tem = null;

            ToDoi_NguoiChoi?.Clear();
            ToDoi_NguoiChoi = null;

            ToDoi_NguoiChoi_Da_OffLine?.Clear();
            ToDoi_NguoiChoi_Da_OffLine = null;
            // Safety cleanup using PartyManager
            PartyManager.DissolveParty(TeamID, "party disposed");
			if (AutomaticDisplay != null)
			{
				AutomaticDisplay.Enabled = false;
				AutomaticDisplay.Close();
				AutomaticDisplay.Dispose();
				AutomaticDisplay = null;
			}
			Moi_NguoiChoi = null;
			DoiNguDangCap = 0;
			RedPackage = false;
			RedPackageThoiGian = 0;
		}
	}

	private void AutomaticDisplayEvent(object sender, ElapsedEventArgs e)
	{
		var num = 0;
		try
		{
			if (ToDoi_NguoiChoi != null && ToDoi_NguoiChoi.Count <= 1)
			{
				Dispose();
				return;
			}
			GuiLoiMoi_ToDoi_DemSoLuong++;
			if (GuiLoiMoi_ToDoi_DemSoLuong >= 15)
			{
				GuiLoiMoi_ToDoi_DemSoLuong = 0;
				if (ToDoi_NguoiChoi_Da_OffLine != null && ToDoi_NguoiChoi_Da_OffLine.Count > 0)
				{
					List<string> list = new();
					foreach (var value2 in ToDoi_NguoiChoi_Da_OffLine.Values)
					{
						var players = World.KiemTra_Ten_NguoiChoi(value2.UserName);
						if (players == null)
						{
							continue;
						}
						if (value2.TeamID == TeamID)
						{
							if (ToDoi_NguoiChoi.Count >= World.Gioi_han_so_nguoi_vao_party)
							{
								continue;
							}
							if (DoiTruong.FindPlayers(1000, players))
							{
								if (players.TeamingStage == 0 && ToDoi_NguoiChoi.Count < World.Gioi_han_so_nguoi_vao_party && players.TeamID == 0)
								{
									var array = Converter.HexStringToByte("AA5528002C0130000600010001002D010000000000000000000000000000000000000000000000000000000055AA");
									System.Buffer.BlockCopy(BitConverter.GetBytes(DoiTruong.SessionID), 0, array, 4, 2);
									System.Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 14, 2);
									DoiTruong.SendRequestTeam(array, array.Length);
								}
								else if (players.TeamingStage == 1)
								{
									var array2 = Converter.HexStringToByte("AA5512002C013200040001002C01000000000000000055AA");
									System.Buffer.BlockCopy(BitConverter.GetBytes(DoiTruong.SessionID), 0, array2, 4, 2);
									System.Buffer.BlockCopy(BitConverter.GetBytes(DoiTruong.SessionID), 0, array2, 12, 2);
									DoiTruong.AbortTeamRequest(array2, array2.Length);
								}
								else if (players.TeamingStage == 2 && !list.Contains(players.AccountID))
								{
									list.Add(players.AccountID);
								}
							}
							else
							{
								players.HeThongNhacNho("Hiệp khách lạc bước quá xa tổ đội, đồng môn của ngươi đang ở [" + X_Toa_Do_Class.getmapname(DoiTruong.MapID) + "] - tọa độ: [" + DoiTruong.PosX + "," + DoiTruong.PosY + "]!", 10, "Thiên cơ các");
							}
						}
						else if (!list.Contains(players.AccountID))
						{
							list.Add(players.AccountID);
						}
					}
					if (list.Count > 0)
					{
						foreach (var item in list)
						{
							if (ToDoi_NguoiChoi_Da_OffLine.ContainsKey(item))
							{
								ToDoi_NguoiChoi_Da_OffLine.TryRemove(item, out _);
							}
						}
					}
					list.Clear();
				}
			}
			if (RedPackage)
			{
				RedPackageThoiGian -= 3000;
				if (RedPackageThoiGian <= 0)
				{
					RedPackage = false;
					RedPackageThoiGian = 0;
				}
			}
			else
			{
				RedPackage = false;
				RedPackageThoiGian = 0;
			}
			Players value;
			if (ToDoi_NguoiChoi != null)
			{
				foreach (var value3 in ToDoi_NguoiChoi.Values)
				{
					if (World.allConnectedChars.TryGetValue(value3.SessionID, out value))
					{
						value3.ShowPlayers();
						if (RedPackage && RedPackageThoiGian > 0)
						{
							if (value3.AppendStatusList != null && !value3.GetAddState(**********))
							{
								StatusEffect x_Them_Vao_Trang_Thai_Loai = new(value3, RedPackageThoiGian, **********, 0);
								value3.AppendStatusList.Add(x_Them_Vao_Trang_Thai_Loai.FLD_PID, x_Them_Vao_Trang_Thai_Loai);
								value3.StatusEffect(BitConverter.GetBytes(**********), 1, RedPackageThoiGian);
							}
						}
						else if (value3.AppendStatusList != null && value3.GetAddState(**********))
						{
							value3.AppendStatusList[**********].ThoiGianKetThucSuKien();
						}
					}
					else if (tem != null && !tem.Contains(value3))
					{
						tem.Add(value3);
					}
				}
			}
			if (tem != null)
			{
				foreach (var item2 in tem)
				{
					if (ToDoi_NguoiChoi != null && ToDoi_NguoiChoi.TryGetValue(item2.SessionID, out value))
					{
						ToDoi_NguoiChoi.TryRemove(item2.SessionID, out _);
						item2.TeamID = 0;
						item2.TeamingStage = 0;
					}
				}
			}
			if (tem.Count > 0)
			{
				tem.Clear();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Auto Party tạo nhóm Tổ Đội bị lỗi - [" + num + "] - [" + ex.Message);
		}
	}

	public void UyQuyen_DoiTruong(Players Old_DoiTruong, Players New_DoiTruong)
	{
		try
		{
			DoiTruongTen = New_DoiTruong.CharacterName;
			DoiTruong = New_DoiTruong;
			foreach (var value in ToDoi_NguoiChoi.Values)
			{
				value.UyQuyen_DoiTruong_NhacNho(Old_DoiTruong, New_DoiTruong);
				value.ShowPlayers();
			}

		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Chuyển quyền Key đội trưởng Lỗi !! - " + ex.Message);
		}
	}

	public void ThamGiaThanhVienNhom_NhacNho(Players NguoiChoi)
	{
		try
		{
			if (NguoiChoi.FLD_Couple.Length != 0)
			{
				foreach (var value2 in ToDoi_NguoiChoi.Values)
				{
					if (value2.CharacterName == NguoiChoi.FLD_Couple)
					{
						NguoiChoi.CoupleInTeam = true;
						value2.CoupleInTeam = true;
						break;
					}
				}
			}
			foreach (var value3 in ToDoi_NguoiChoi.Values)
			{
				if (NguoiChoi != value3)
				{
					value3.GiaNhap_ToDoi_NhacNho(NguoiChoi);
					NguoiChoi.GiaNhap_ToDoi_NhacNho(value3);
				}
				value3.ShowPlayers();
			}
			if (ToDoi_NguoiChoi.Count >= 2)
			{
				AutomaticDisplay.Enabled = true;
			}
			if (ToDoi_NguoiChoi_Da_OffLine.TryGetValue(NguoiChoi.AccountID, out var _))
			{
				ToDoi_NguoiChoi_Da_OffLine.TryRemove(NguoiChoi.AccountID, out _);
			}

		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "ToDoi tốt bụng ThamGiaThanhVienNhom_NhacNho error!" + ex.Message);
		}
	}

	public void LeaveParty(Players NguoiChoi, int Exit_ID)
	{
		var num = 0;
		try
		{
			if (ToDoi_NguoiChoi != null && ToDoi_NguoiChoi.ContainsKey(NguoiChoi.SessionID))
			{
				// Use PartyManager for leave handling
				// Exit_ID == 0: voluntary leave
				// Exit_ID == 1: involuntary disconnect
				var reason = Exit_ID == 0 ? PartyLeaveReason.Voluntary : PartyLeaveReason.Disconnect;
				PartyManager.LeaveParty(NguoiChoi, reason);

				ToDoi_NguoiChoi.TryRemove(NguoiChoi.SessionID, out _);

				// DISABLED: Old offline player tracking system - replaced by RestoredParties
				// if (Exit_ID == 1 && ToDoi_NguoiChoi.Count >= 2 && !ToDoi_NguoiChoi_Da_OffLine.ContainsKey(NguoiChoi.AccountID))
				// {
				// 	ToDoi_NguoiChoi_Da_OffLine.TryAdd(NguoiChoi.AccountID, new()
				// 	{
				// 		TeamID = NguoiChoi.TeamID,
				// 		UserName = NguoiChoi.CharacterName
				// 	});
				// }
			}
			if (NguoiChoi.GetAddState(**********))
			{
				NguoiChoi.AppendStatusList[**********].ThoiGianKetThucSuKien();
			}
			num = 1;
			if (NguoiChoi.FLD_Couple.Length != 0)
			{
				NguoiChoi.CoupleInTeam = false;
				num = 3;
				if (ToDoi_NguoiChoi != null)
				{
					foreach (var value in ToDoi_NguoiChoi.Values)
					{
						num = 4;
						if (value.CharacterName == NguoiChoi.FLD_Couple)
						{
							num = 5;
							value.CoupleInTeam = false;
							num = 6;
							break;
						}
					}
				}
			}
			num = 7;
			if (ToDoi_NguoiChoi != null && ToDoi_NguoiChoi.Count >= 1)
			{
				num = 8;
				if (DoiTruongTen!= NguoiChoi.CharacterName)
				{
					foreach (var value2 in ToDoi_NguoiChoi.Values)
					{
						num = 9;
						value2.RoiKhoi_ToDoi_NhacNho(NguoiChoi);
						num = 10;
						value2.ShowPlayers();
						num = 11;
					}
				}
				else
				{
					var flag = true;
					foreach (var value3 in ToDoi_NguoiChoi.Values)
					{
						if (flag)
						{
							num = 12;
							UyQuyen_DoiTruong(NguoiChoi, value3);
							flag = false;
						}
						num = 15;
						value3.HeThongNhacNho("Đội trưởng đã được truyền cho " + DoiTruongTen + "!", 7, "Thiên Cơ Lệnh");
						value3.RoiKhoi_ToDoi_NhacNho(NguoiChoi);
						value3.ShowPlayers();
						num = 16;
					}
				}
			}
			else
			{
				num = 17;
				Dispose();
			}
			num = 18;
			NguoiChoi.this_RoiKhoi_ToDoi_NhacNho();
			num = 19;
			NguoiChoi.TeamID = 0;
			num = 20;
			NguoiChoi.TeamingStage = 0;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Tổ Đội Party Thoát bị error! |" + num + "|" + ex.Message);
		}
		finally
		{
			NguoiChoi.TeamID = 0;
			NguoiChoi.TeamingStage = 0;
		}
	}

	// Legacy RestoredParties integration removed - now handled by PartyManager
}
