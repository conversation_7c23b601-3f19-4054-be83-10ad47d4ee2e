using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using RxjhServer.Database.FreeSql.Entities.Game;

namespace RxjhServer;

/// <summary>
/// Database operations for persistent parties
/// </summary>
public static class PartyDatabase
{
    /// <summary>
    /// Load all persistent parties for a server
    /// </summary>
    public static async Task<List<X_To_Doi_Class>> LoadPersistentPartiesAsync(int serverId)
    {
        try
        {
            var dbParties = await GameDb.FreeSql.Select<tbl_persistent_parties>()
                .Where(p => p.server_id == serverId && p.is_active == true)
                .Where(p => p.last_active_at > DateTime.Now.AddHours(-24)) // Not expired
                .ToListAsync();

            var result = new List<X_To_Doi_Class>();

            foreach (var dbParty in dbParties)
            {
                try
                {
                    var party = DeserializeParty(dbParty);
                    if (party != null && IsValidParty(party))
                    {
                        result.Add(party);
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Error deserializing party {dbParty.party_uuid}: {ex.Message}");
                }
            }

            Logger.Instance.Info($"✓ Loaded {result.Count} persistent parties for server {serverId}");
            return result;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to load persistent parties: {ex.Message}");
            return new List<X_To_Doi_Class>();
        }
    }

    /// <summary>
    /// Save party to database
    /// </summary>
    public static async Task SavePartyAsync(X_To_Doi_Class party)
    {
        try
        {
            if (party == null || string.IsNullOrEmpty(party.PartyUUID))
            {
                LogHelper.WriteLine(LogLevel.Warning, "Cannot save null party or party without UUID");
                return;
            }

            var membersJson = SerializeMembers(party);
            var totalMembers = party.ToDoi_NguoiChoi.Count + party.OfflineMembers.Count;

            // Only save if party has potential (at least 1 member)
            if (totalMembers == 0)
            {
                LogHelper.WriteLine(LogLevel.Debug, $"Skipping save for empty party {party.PartyUUID}");
                return;
            }
            try
            {
                var affectedRows = await GameDb.FreeSql.InsertOrUpdate<tbl_persistent_parties>()
                    .SetSource(new tbl_persistent_parties
                    {
                        party_uuid = party.PartyUUID,
                        team_id = party.TeamID,
                        server_id = party.ServerId,
                        leader_name = party.DoiTruongTen,
                        loot_type = party.DaoCuQuyTac_PhanPhoi,
                        max_members = World.Gioi_han_so_nguoi_vao_party,
                        persistence_mode = (int)party.PersistenceMode,
                        created_at = party.CreatedAt,
                        last_active_at = party.LastActiveAt,
                        is_active = true,
                        members = membersJson
                    })
                    .ExecuteAffrowsAsync();
                LogHelper.WriteLine(LogLevel.Info, $"Saved party {party.PartyUUID}: {party.ToDoi_NguoiChoi.Count} online, {party.OfflineMembers.Count} offline members");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error saving party {party.PartyUUID}: {ex.Message}");
            }

            // Use PostgreSQL UPSERT (ON CONFLICT) for atomic operation
            // try
            // {
            //     var affectedRows = await GameDb.FreeSql.Insert<tbl_persistent_parties>()
            //         .AppendData(new tbl_persistent_parties
            //         {
            //             party_uuid = party.PartyUUID,
            //             team_id = party.TeamID,
            //             server_id = party.ServerId,
            //             leader_name = party.DoiTruongTen,
            //             loot_type = party.DaoCuQuyTac_PhanPhoi,
            //             max_members = World.Gioi_han_so_nguoi_vao_party,
            //             persistence_mode = (int)party.PersistenceMode,
            //             created_at = party.CreatedAt,
            //             last_active_at = party.LastActiveAt,
            //             is_active = true,
            //             members = membersJson
            //         })
            //         .OnConflictDoUpdate()
            //         .ExecuteAffrowsAsync();
            // }
            // catch (Exception insertEx)
            // {
            //     // Fallback to update if insert fails
            //     LogHelper.WriteLine(LogLevel.Debug, $"Insert failed for party {party.PartyUUID}, trying update: {insertEx.Message}");

            //     var affectedRows = await GameDb.FreeSql.Update<tbl_persistent_parties>()
            //         .Set(p => p.team_id, party.TeamID)
            //         .Set(p => p.server_id, party.ServerId)
            //         .Set(p => p.leader_name, party.DoiTruongTen)
            //         .Set(p => p.loot_type, party.DaoCuQuyTac_PhanPhoi)
            //         .Set(p => p.max_members, World.Gioi_han_so_nguoi_vao_party)
            //         .Set(p => p.persistence_mode, (int)party.PersistenceMode)
            //         .Set(p => p.last_active_at, party.LastActiveAt)
            //         .Set(p => p.is_active, true)
            //         .Set(p => p.members, membersJson)
            //         .Where(p => p.party_uuid == party.PartyUUID)
            //         .ExecuteAffrowsAsync();
            // }

           
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"✗ Failed to save party {party.PartyUUID}: {ex.Message}");
        }
    }


    /// <summary>
    /// Serialize party members to JSON
    /// </summary>
    private static string SerializeMembers(X_To_Doi_Class party)
    {
        try
        {
            var data = new PartyMembersJson();

            // Serialize online members
            foreach (var player in party.ToDoi_NguoiChoi.Values)
            {
                data.online.Add(new OnlinePartyMemberData
                {
                    characterName = player.CharacterName,
                    accountId = int.Parse(player.AccountID),
                    joinOrder = 1, // TODO: Track actual join order
                    joinedAt = DateTime.Now, // TODO: Track actual join time
                    isLeader = player.CharacterName == party.DoiTruongTen,
                    level = player.Player_Level,
                    job = player.Player_Job,
                    serverId = World.ServerID,
                    sessionId = player.SessionID
                });
            }

            // Serialize offline members
            foreach (var offlineMember in party.OfflineMembers.Values)
            {
                data.offline.Add(new OfflinePartyMemberData
                {
                    characterName = offlineMember.CharacterName,
                    accountId = offlineMember.AccountId,
                    joinOrder = offlineMember.OriginalJoinOrder,
                    joinedAt = offlineMember.JoinedAt,
                    disconnectedAt = offlineMember.DisconnectedAt,
                    isLeader = offlineMember.IsLeader,
                    level = offlineMember.Level,
                    job = offlineMember.Job,
                    serverId = offlineMember.ServerId
                });
            }

            return JsonSerializer.Serialize(data);
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Error serializing party members: {ex.Message}");
            return "{}";
        }
    }

    /// <summary>
    /// Deserialize party from database record
    /// </summary>
    private static X_To_Doi_Class DeserializeParty(tbl_persistent_parties dbParty)
    {
        try
        {
            // Parse members JSON
            var membersData = JsonSerializer.Deserialize<PartyMembersJson>(dbParty.members);
            if (membersData == null)
                return null;

            // Create party with dummy leader (will be updated)
            var dummyLeader = new Players(); // TODO: Create proper dummy player
            var party = new X_To_Doi_Class(dummyLeader)
            {
                PartyUUID = dbParty.party_uuid,
                TeamID = dbParty.team_id ?? 0,
                ServerId = dbParty.server_id,
                DoiTruongTen = dbParty.leader_name,
                DaoCuQuyTac_PhanPhoi = dbParty.loot_type,
                PersistenceMode = (PartyPersistenceMode)dbParty.persistence_mode,
                CreatedAt = dbParty.created_at,
                LastActiveAt = dbParty.last_active_at
            };

            // Clear dummy leader
            party.ToDoi_NguoiChoi.Clear();

            // Restore offline members
            foreach (var offlineData in membersData.offline)
            {
                party.OfflineMembers.TryAdd(offlineData.characterName, new OfflinePartyMember
                {
                    CharacterName = offlineData.characterName,
                    AccountId = offlineData.accountId,
                    OriginalJoinOrder = offlineData.joinOrder,
                    JoinedAt = offlineData.joinedAt,
                    DisconnectedAt = offlineData.disconnectedAt,
                    IsLeader = offlineData.isLeader,
                    ServerId = offlineData.serverId,
                    Level = offlineData.level,
                    Job = offlineData.job
                });
            }

            // Note: Online members will be restored when players reconnect

            return party;
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Error deserializing party {dbParty.party_uuid}: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// Check if party is valid for loading
    /// </summary>
    private static bool IsValidParty(X_To_Doi_Class party)
    {
        // Must have at least 2 total members (online + offline)
        if (party.TotalMemberCount < 2)
            return false;

        // Must not be expired
        if (DateTime.Now - party.LastActiveAt > TimeSpan.FromHours(24))
            return false;

        // Must have valid UUID
        if (string.IsNullOrEmpty(party.PartyUUID))
            return false;

        return true;
    }

    /// <summary>
    /// Clean up expired parties in database
    /// </summary>
    public static async Task CleanupExpiredPartiesAsync()
    {
        try
        {
            var expiredTime = DateTime.Now.AddHours(-24);

            var affectedRows = await GameDb.FreeSql.Update<tbl_persistent_parties>()
                .Set(p => p.is_active, false)
                .Where(p => p.last_active_at < expiredTime && p.is_active == true)
                .ExecuteAffrowsAsync();

            if (affectedRows > 0)
            {
                LogHelper.WriteLine(LogLevel.Info, $"Cleaned up {affectedRows} expired parties from database");
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Error cleaning up expired parties: {ex.Message}");
        }
    }

    /// <summary>
    /// Get party statistics from database
    /// </summary>
    public static async Task<Dictionary<string, object>> GetDatabaseStatisticsAsync()
    {
        try
        {
            var stats = new Dictionary<string, object>();

            // Total parties
            stats["total_parties"] = await GameDb.FreeSql.Select<tbl_persistent_parties>()
                .CountAsync();

            // Active parties
            stats["active_parties"] = await GameDb.FreeSql.Select<tbl_persistent_parties>()
                .Where(p => p.is_active == true)
                .CountAsync();

            // Parties by server
            var partiesByServer = await GameDb.FreeSql.Select<tbl_persistent_parties>()
                .Where(p => p.is_active == true)
                .GroupBy(p => p.server_id)
                .ToListAsync(g => new { ServerId = g.Key, Count = g.Count() });

            stats["parties_by_server"] = partiesByServer.ToDictionary(x => x.ServerId.ToString(), x => (object)x.Count);

            // Expired parties
            var expiredTime = DateTime.Now.AddHours(-24);
            stats["expired_parties"] = await GameDb.FreeSql.Select<tbl_persistent_parties>()
                .Where(p => p.is_active == true && p.last_active_at < expiredTime)
                .CountAsync();

            return stats;
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Error getting database statistics: {ex.Message}");
            return new Dictionary<string, object>();
        }
    }
}
