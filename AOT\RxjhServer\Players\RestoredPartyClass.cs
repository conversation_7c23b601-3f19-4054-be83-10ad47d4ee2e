
using System;
using System.Collections.Generic;
using System.Linq;

namespace RxjhServer;
/// <summary>
/// Class lưu trữ thông tin party đã được restore từ database
/// Chờ players rejoin để tái tạo party thực tế
/// </summary>
public class RestoredPartyData
{
    /// <summary>
    /// UUID của party
    /// </summary>
    public string PartyUUID { get; set; }

    /// <summary>
    /// Tên leader gốc
    /// </summary>
    public string OriginalLeaderName { get; set; }

    /// <summary>
    /// Tên leader hiện tại
    /// </summary>
    public string CurrentLeaderName { get; set; }

    /// <summary>
    /// Loại phân phối loot
    /// </summary>
    public int LootType { get; set; }

    /// <summary>
    /// Danh sách thành viên party (character name + account id)
    /// </summary>
    public List<RestoredPartyMember> Members { get; set; } = new List<RestoredPartyMember>();

    /// <summary>
    /// Thời gian restore
    /// </summary>
    public DateTime RestoredAt { get; set; }

    /// <summary>
    /// Thời gian hoạt động cuối cùng
    /// </summary>
    public DateTime LastActiveAt { get; set; }

    /// <summary>
    /// Số lượng thành viên tối đa
    /// </summary>
    public int MaxMembers { get; set; }

    /// <summary>
    /// Server ID
    /// </summary>
    public int ServerId { get; set; }

    /// <summary>
    /// TeamID của party hiện tại (nếu đã được tái tạo)
    /// </summary>
    public int? ActiveTeamID { get; set; }

    /// <summary>
    /// Kiểm tra xem party có hết hạn không (quá 24 giờ không hoạt động)
    /// </summary>
    public bool IsExpired => DateTime.Now - LastActiveAt > TimeSpan.FromHours(24);

    /// <summary>
    /// Số lượng thành viên đã rejoin
    /// </summary>
    public int RejoinedMembersCount => Members.Count(m => m.HasRejoined);

    /// <summary>
    /// Kiểm tra xem party đã được tái tạo thành công chưa
    /// </summary>
    public bool IsActiveParty => ActiveTeamID.HasValue && ActiveTeamID.Value > 0;

    /// <summary>
    /// Kiểm tra xem có thể tạo party thực tế không (có ít nhất 1 thành viên online)
    /// </summary>
    public bool CanCreateRealParty => RejoinedMembersCount > 0;
}

/// <summary>
/// Thông tin thành viên party đã restore
/// </summary>
public class RestoredPartyMember
{
    /// <summary>
    /// Tên nhân vật
    /// </summary>
    public string CharacterName { get; set; }

    /// <summary>
    /// Account ID
    /// </summary>
    public int AccountId { get; set; }

    /// <summary>
    /// Thứ tự join ban đầu
    /// </summary>
    public int OriginalJoinOrder { get; set; }

    /// <summary>
    /// Thứ tự rejoin (first-come-first-served)
    /// </summary>
    public int? RejoinOrder { get; set; }

    /// <summary>
    /// Thời gian join ban đầu
    /// </summary>
    public DateTime JoinedAt { get; set; }

    /// <summary>
    /// Thời gian rejoin
    /// </summary>
    public DateTime? RejoinedAt { get; set; }

    /// <summary>
    /// Có phải leader không
    /// </summary>
    public bool IsLeader { get; set; }

    /// <summary>
    /// Đã rejoin chưa
    /// </summary>
    public bool HasRejoined => RejoinedAt.HasValue;

    /// <summary>
    /// Player object hiện tại (nếu đã rejoin)
    /// </summary>
    public Players CurrentPlayer { get; set; }
}