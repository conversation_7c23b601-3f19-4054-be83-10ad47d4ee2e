﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_xwwl_kongfu {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('tbl_xwwl_kongfu_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty]
		public int? fld_pid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty]
		public int? fld_source_at { get; set; }

		[JsonProperty]
		public int? fld_at { get; set; }

		[JsonProperty]
		public int? fld_mp { get; set; }

		[JsonProperty]
		public int? fld_level { get; set; }

		[JsonProperty]
		public int? fld_needexp { get; set; }

		[JsonProperty]
		public int? fld_job { get; set; }

		[JsonProperty]
		public int? fld_zx { get; set; }

		[JsonProperty]
		public int? fld_joblevel { get; set; }

		[JsonProperty]
		public int? fld_type { get; set; }

		[JsonProperty]
		public int? fld_effert { get; set; }

		[JsonProperty]
		public int? fld_index { get; set; }

		[JsonProperty]
		public int? fld_congkichsoluong { get; set; }

		[JsonProperty]
		public int? fld_vocongloaihinh { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_moicapnguyhai { get; set; }

		[JsonProperty]
		public int? fld_moicapthemnguyhai { get; set; }

		[JsonProperty]
		public int? fld_moicapthemmp { get; set; }

		[JsonProperty]
		public int? fld_moicapthemlichluyen { get; set; }

		[JsonProperty]
		public int? fld_moicapthemtuluyendangcap { get; set; }

		[JsonProperty]
		public int? fld_moicapvocongdiemso { get; set; }

		[JsonProperty]
		public int? fld_vocongtoicaodangcap { get; set; }

		[JsonProperty]
		public int? fld_time { get; set; }

		[JsonProperty]
		public int? fld_deathtime { get; set; }

		[JsonProperty]
		public int? fld_cdtime { get; set; }

		[JsonProperty]
		public int? time_animation { get; set; }

	}

}
